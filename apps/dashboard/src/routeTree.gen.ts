/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as CommonRouteImport } from './routes/_common'
import { Route as AnonRouteImport } from './routes/_anon'
import { Route as CommonSettingsRouteImport } from './routes/_common/settings'
import { Route as CommonNotificationsRouteImport } from './routes/_common/notifications'
import { Route as CommonFaqRouteImport } from './routes/_common/faq'
import { Route as CommonComplaintsRequestsRouteImport } from './routes/_common/complaints-requests'
import { Route as CommonSubscriptionsRouteImport } from './routes/_common/_subscriptions'
import { Route as AnonLoginRouteImport } from './routes/_anon/login'
import { Route as CommonSettingsIndexRouteImport } from './routes/_common/settings/index'
import { Route as CommonComplaintsRequestsIndexRouteImport } from './routes/_common/complaints-requests/index'
import { Route as CommonSubscriptionsIndexRouteImport } from './routes/_common/_subscriptions/index'
import { Route as CommonSettingsNotificationsRouteImport } from './routes/_common/settings/notifications'
import { Route as CommonComplaintsRequestsComplaintIdRouteImport } from './routes/_common/complaints-requests/$complaintId'
import { Route as CommonSubscriptionsSubscriptionIdRouteImport } from './routes/_common/_subscriptions/$subscriptionId'
import { Route as CommonSubscriptionsSubscriptionIdIndexRouteImport } from './routes/_common/_subscriptions/$subscriptionId/index'
import { Route as CommonSubscriptionsSubscriptionIdQueryRouteImport } from './routes/_common/_subscriptions/$subscriptionId/query'
import { Route as CommonSubscriptionsSubscriptionIdOutagesRouteImport } from './routes/_common/_subscriptions/$subscriptionId/outages'
import { Route as CommonSubscriptionsSubscriptionIdNotificationsRouteImport } from './routes/_common/_subscriptions/$subscriptionId/notifications'

const CommonRoute = CommonRouteImport.update({
  id: '/_common',
  getParentRoute: () => rootRouteImport,
} as any)
const AnonRoute = AnonRouteImport.update({
  id: '/_anon',
  getParentRoute: () => rootRouteImport,
} as any)
const CommonSettingsRoute = CommonSettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => CommonRoute,
} as any)
const CommonNotificationsRoute = CommonNotificationsRouteImport.update({
  id: '/notifications',
  path: '/notifications',
  getParentRoute: () => CommonRoute,
} as any)
const CommonFaqRoute = CommonFaqRouteImport.update({
  id: '/faq',
  path: '/faq',
  getParentRoute: () => CommonRoute,
} as any)
const CommonComplaintsRequestsRoute =
  CommonComplaintsRequestsRouteImport.update({
    id: '/complaints-requests',
    path: '/complaints-requests',
    getParentRoute: () => CommonRoute,
  } as any)
const CommonSubscriptionsRoute = CommonSubscriptionsRouteImport.update({
  id: '/_subscriptions',
  getParentRoute: () => CommonRoute,
} as any)
const AnonLoginRoute = AnonLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AnonRoute,
} as any)
const CommonSettingsIndexRoute = CommonSettingsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => CommonSettingsRoute,
} as any)
const CommonComplaintsRequestsIndexRoute =
  CommonComplaintsRequestsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => CommonComplaintsRequestsRoute,
  } as any)
const CommonSubscriptionsIndexRoute =
  CommonSubscriptionsIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => CommonSubscriptionsRoute,
  } as any)
const CommonSettingsNotificationsRoute =
  CommonSettingsNotificationsRouteImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => CommonSettingsRoute,
  } as any)
const CommonComplaintsRequestsComplaintIdRoute =
  CommonComplaintsRequestsComplaintIdRouteImport.update({
    id: '/$complaintId',
    path: '/$complaintId',
    getParentRoute: () => CommonComplaintsRequestsRoute,
  } as any)
const CommonSubscriptionsSubscriptionIdRoute =
  CommonSubscriptionsSubscriptionIdRouteImport.update({
    id: '/$subscriptionId',
    path: '/$subscriptionId',
    getParentRoute: () => CommonSubscriptionsRoute,
  } as any)
const CommonSubscriptionsSubscriptionIdIndexRoute =
  CommonSubscriptionsSubscriptionIdIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => CommonSubscriptionsSubscriptionIdRoute,
  } as any)
const CommonSubscriptionsSubscriptionIdQueryRoute =
  CommonSubscriptionsSubscriptionIdQueryRouteImport.update({
    id: '/query',
    path: '/query',
    getParentRoute: () => CommonSubscriptionsSubscriptionIdRoute,
  } as any)
const CommonSubscriptionsSubscriptionIdOutagesRoute =
  CommonSubscriptionsSubscriptionIdOutagesRouteImport.update({
    id: '/outages',
    path: '/outages',
    getParentRoute: () => CommonSubscriptionsSubscriptionIdRoute,
  } as any)
const CommonSubscriptionsSubscriptionIdNotificationsRoute =
  CommonSubscriptionsSubscriptionIdNotificationsRouteImport.update({
    id: '/notifications',
    path: '/notifications',
    getParentRoute: () => CommonSubscriptionsSubscriptionIdRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/login': typeof AnonLoginRoute
  '/complaints-requests': typeof CommonComplaintsRequestsRouteWithChildren
  '/faq': typeof CommonFaqRoute
  '/notifications': typeof CommonNotificationsRoute
  '/settings': typeof CommonSettingsRouteWithChildren
  '/$subscriptionId': typeof CommonSubscriptionsSubscriptionIdRouteWithChildren
  '/complaints-requests/$complaintId': typeof CommonComplaintsRequestsComplaintIdRoute
  '/settings/notifications': typeof CommonSettingsNotificationsRoute
  '/': typeof CommonSubscriptionsIndexRoute
  '/complaints-requests/': typeof CommonComplaintsRequestsIndexRoute
  '/settings/': typeof CommonSettingsIndexRoute
  '/$subscriptionId/notifications': typeof CommonSubscriptionsSubscriptionIdNotificationsRoute
  '/$subscriptionId/outages': typeof CommonSubscriptionsSubscriptionIdOutagesRoute
  '/$subscriptionId/query': typeof CommonSubscriptionsSubscriptionIdQueryRoute
  '/$subscriptionId/': typeof CommonSubscriptionsSubscriptionIdIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof AnonLoginRoute
  '/faq': typeof CommonFaqRoute
  '/notifications': typeof CommonNotificationsRoute
  '/complaints-requests/$complaintId': typeof CommonComplaintsRequestsComplaintIdRoute
  '/settings/notifications': typeof CommonSettingsNotificationsRoute
  '/': typeof CommonSubscriptionsIndexRoute
  '/complaints-requests': typeof CommonComplaintsRequestsIndexRoute
  '/settings': typeof CommonSettingsIndexRoute
  '/$subscriptionId/notifications': typeof CommonSubscriptionsSubscriptionIdNotificationsRoute
  '/$subscriptionId/outages': typeof CommonSubscriptionsSubscriptionIdOutagesRoute
  '/$subscriptionId/query': typeof CommonSubscriptionsSubscriptionIdQueryRoute
  '/$subscriptionId': typeof CommonSubscriptionsSubscriptionIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_anon': typeof AnonRouteWithChildren
  '/_common': typeof CommonRouteWithChildren
  '/_anon/login': typeof AnonLoginRoute
  '/_common/_subscriptions': typeof CommonSubscriptionsRouteWithChildren
  '/_common/complaints-requests': typeof CommonComplaintsRequestsRouteWithChildren
  '/_common/faq': typeof CommonFaqRoute
  '/_common/notifications': typeof CommonNotificationsRoute
  '/_common/settings': typeof CommonSettingsRouteWithChildren
  '/_common/_subscriptions/$subscriptionId': typeof CommonSubscriptionsSubscriptionIdRouteWithChildren
  '/_common/complaints-requests/$complaintId': typeof CommonComplaintsRequestsComplaintIdRoute
  '/_common/settings/notifications': typeof CommonSettingsNotificationsRoute
  '/_common/_subscriptions/': typeof CommonSubscriptionsIndexRoute
  '/_common/complaints-requests/': typeof CommonComplaintsRequestsIndexRoute
  '/_common/settings/': typeof CommonSettingsIndexRoute
  '/_common/_subscriptions/$subscriptionId/notifications': typeof CommonSubscriptionsSubscriptionIdNotificationsRoute
  '/_common/_subscriptions/$subscriptionId/outages': typeof CommonSubscriptionsSubscriptionIdOutagesRoute
  '/_common/_subscriptions/$subscriptionId/query': typeof CommonSubscriptionsSubscriptionIdQueryRoute
  '/_common/_subscriptions/$subscriptionId/': typeof CommonSubscriptionsSubscriptionIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/complaints-requests'
    | '/faq'
    | '/notifications'
    | '/settings'
    | '/$subscriptionId'
    | '/complaints-requests/$complaintId'
    | '/settings/notifications'
    | '/'
    | '/complaints-requests/'
    | '/settings/'
    | '/$subscriptionId/notifications'
    | '/$subscriptionId/outages'
    | '/$subscriptionId/query'
    | '/$subscriptionId/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/faq'
    | '/notifications'
    | '/complaints-requests/$complaintId'
    | '/settings/notifications'
    | '/'
    | '/complaints-requests'
    | '/settings'
    | '/$subscriptionId/notifications'
    | '/$subscriptionId/outages'
    | '/$subscriptionId/query'
    | '/$subscriptionId'
  id:
    | '__root__'
    | '/_anon'
    | '/_common'
    | '/_anon/login'
    | '/_common/_subscriptions'
    | '/_common/complaints-requests'
    | '/_common/faq'
    | '/_common/notifications'
    | '/_common/settings'
    | '/_common/_subscriptions/$subscriptionId'
    | '/_common/complaints-requests/$complaintId'
    | '/_common/settings/notifications'
    | '/_common/_subscriptions/'
    | '/_common/complaints-requests/'
    | '/_common/settings/'
    | '/_common/_subscriptions/$subscriptionId/notifications'
    | '/_common/_subscriptions/$subscriptionId/outages'
    | '/_common/_subscriptions/$subscriptionId/query'
    | '/_common/_subscriptions/$subscriptionId/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AnonRoute: typeof AnonRouteWithChildren
  CommonRoute: typeof CommonRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_common': {
      id: '/_common'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof CommonRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_anon': {
      id: '/_anon'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AnonRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_common/settings': {
      id: '/_common/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof CommonSettingsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/notifications': {
      id: '/_common/notifications'
      path: '/notifications'
      fullPath: '/notifications'
      preLoaderRoute: typeof CommonNotificationsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/faq': {
      id: '/_common/faq'
      path: '/faq'
      fullPath: '/faq'
      preLoaderRoute: typeof CommonFaqRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/complaints-requests': {
      id: '/_common/complaints-requests'
      path: '/complaints-requests'
      fullPath: '/complaints-requests'
      preLoaderRoute: typeof CommonComplaintsRequestsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_common/_subscriptions': {
      id: '/_common/_subscriptions'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof CommonSubscriptionsRouteImport
      parentRoute: typeof CommonRoute
    }
    '/_anon/login': {
      id: '/_anon/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AnonLoginRouteImport
      parentRoute: typeof AnonRoute
    }
    '/_common/settings/': {
      id: '/_common/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof CommonSettingsIndexRouteImport
      parentRoute: typeof CommonSettingsRoute
    }
    '/_common/complaints-requests/': {
      id: '/_common/complaints-requests/'
      path: '/'
      fullPath: '/complaints-requests/'
      preLoaderRoute: typeof CommonComplaintsRequestsIndexRouteImport
      parentRoute: typeof CommonComplaintsRequestsRoute
    }
    '/_common/_subscriptions/': {
      id: '/_common/_subscriptions/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof CommonSubscriptionsIndexRouteImport
      parentRoute: typeof CommonSubscriptionsRoute
    }
    '/_common/settings/notifications': {
      id: '/_common/settings/notifications'
      path: '/notifications'
      fullPath: '/settings/notifications'
      preLoaderRoute: typeof CommonSettingsNotificationsRouteImport
      parentRoute: typeof CommonSettingsRoute
    }
    '/_common/complaints-requests/$complaintId': {
      id: '/_common/complaints-requests/$complaintId'
      path: '/$complaintId'
      fullPath: '/complaints-requests/$complaintId'
      preLoaderRoute: typeof CommonComplaintsRequestsComplaintIdRouteImport
      parentRoute: typeof CommonComplaintsRequestsRoute
    }
    '/_common/_subscriptions/$subscriptionId': {
      id: '/_common/_subscriptions/$subscriptionId'
      path: '/$subscriptionId'
      fullPath: '/$subscriptionId'
      preLoaderRoute: typeof CommonSubscriptionsSubscriptionIdRouteImport
      parentRoute: typeof CommonSubscriptionsRoute
    }
    '/_common/_subscriptions/$subscriptionId/': {
      id: '/_common/_subscriptions/$subscriptionId/'
      path: '/'
      fullPath: '/$subscriptionId/'
      preLoaderRoute: typeof CommonSubscriptionsSubscriptionIdIndexRouteImport
      parentRoute: typeof CommonSubscriptionsSubscriptionIdRoute
    }
    '/_common/_subscriptions/$subscriptionId/query': {
      id: '/_common/_subscriptions/$subscriptionId/query'
      path: '/query'
      fullPath: '/$subscriptionId/query'
      preLoaderRoute: typeof CommonSubscriptionsSubscriptionIdQueryRouteImport
      parentRoute: typeof CommonSubscriptionsSubscriptionIdRoute
    }
    '/_common/_subscriptions/$subscriptionId/outages': {
      id: '/_common/_subscriptions/$subscriptionId/outages'
      path: '/outages'
      fullPath: '/$subscriptionId/outages'
      preLoaderRoute: typeof CommonSubscriptionsSubscriptionIdOutagesRouteImport
      parentRoute: typeof CommonSubscriptionsSubscriptionIdRoute
    }
    '/_common/_subscriptions/$subscriptionId/notifications': {
      id: '/_common/_subscriptions/$subscriptionId/notifications'
      path: '/notifications'
      fullPath: '/$subscriptionId/notifications'
      preLoaderRoute: typeof CommonSubscriptionsSubscriptionIdNotificationsRouteImport
      parentRoute: typeof CommonSubscriptionsSubscriptionIdRoute
    }
  }
}

interface AnonRouteChildren {
  AnonLoginRoute: typeof AnonLoginRoute
}

const AnonRouteChildren: AnonRouteChildren = {
  AnonLoginRoute: AnonLoginRoute,
}

const AnonRouteWithChildren = AnonRoute._addFileChildren(AnonRouteChildren)

interface CommonSubscriptionsSubscriptionIdRouteChildren {
  CommonSubscriptionsSubscriptionIdNotificationsRoute: typeof CommonSubscriptionsSubscriptionIdNotificationsRoute
  CommonSubscriptionsSubscriptionIdOutagesRoute: typeof CommonSubscriptionsSubscriptionIdOutagesRoute
  CommonSubscriptionsSubscriptionIdQueryRoute: typeof CommonSubscriptionsSubscriptionIdQueryRoute
  CommonSubscriptionsSubscriptionIdIndexRoute: typeof CommonSubscriptionsSubscriptionIdIndexRoute
}

const CommonSubscriptionsSubscriptionIdRouteChildren: CommonSubscriptionsSubscriptionIdRouteChildren =
  {
    CommonSubscriptionsSubscriptionIdNotificationsRoute:
      CommonSubscriptionsSubscriptionIdNotificationsRoute,
    CommonSubscriptionsSubscriptionIdOutagesRoute:
      CommonSubscriptionsSubscriptionIdOutagesRoute,
    CommonSubscriptionsSubscriptionIdQueryRoute:
      CommonSubscriptionsSubscriptionIdQueryRoute,
    CommonSubscriptionsSubscriptionIdIndexRoute:
      CommonSubscriptionsSubscriptionIdIndexRoute,
  }

const CommonSubscriptionsSubscriptionIdRouteWithChildren =
  CommonSubscriptionsSubscriptionIdRoute._addFileChildren(
    CommonSubscriptionsSubscriptionIdRouteChildren,
  )

interface CommonSubscriptionsRouteChildren {
  CommonSubscriptionsSubscriptionIdRoute: typeof CommonSubscriptionsSubscriptionIdRouteWithChildren
  CommonSubscriptionsIndexRoute: typeof CommonSubscriptionsIndexRoute
}

const CommonSubscriptionsRouteChildren: CommonSubscriptionsRouteChildren = {
  CommonSubscriptionsSubscriptionIdRoute:
    CommonSubscriptionsSubscriptionIdRouteWithChildren,
  CommonSubscriptionsIndexRoute: CommonSubscriptionsIndexRoute,
}

const CommonSubscriptionsRouteWithChildren =
  CommonSubscriptionsRoute._addFileChildren(CommonSubscriptionsRouteChildren)

interface CommonComplaintsRequestsRouteChildren {
  CommonComplaintsRequestsComplaintIdRoute: typeof CommonComplaintsRequestsComplaintIdRoute
  CommonComplaintsRequestsIndexRoute: typeof CommonComplaintsRequestsIndexRoute
}

const CommonComplaintsRequestsRouteChildren: CommonComplaintsRequestsRouteChildren =
  {
    CommonComplaintsRequestsComplaintIdRoute:
      CommonComplaintsRequestsComplaintIdRoute,
    CommonComplaintsRequestsIndexRoute: CommonComplaintsRequestsIndexRoute,
  }

const CommonComplaintsRequestsRouteWithChildren =
  CommonComplaintsRequestsRoute._addFileChildren(
    CommonComplaintsRequestsRouteChildren,
  )

interface CommonSettingsRouteChildren {
  CommonSettingsNotificationsRoute: typeof CommonSettingsNotificationsRoute
  CommonSettingsIndexRoute: typeof CommonSettingsIndexRoute
}

const CommonSettingsRouteChildren: CommonSettingsRouteChildren = {
  CommonSettingsNotificationsRoute: CommonSettingsNotificationsRoute,
  CommonSettingsIndexRoute: CommonSettingsIndexRoute,
}

const CommonSettingsRouteWithChildren = CommonSettingsRoute._addFileChildren(
  CommonSettingsRouteChildren,
)

interface CommonRouteChildren {
  CommonSubscriptionsRoute: typeof CommonSubscriptionsRouteWithChildren
  CommonComplaintsRequestsRoute: typeof CommonComplaintsRequestsRouteWithChildren
  CommonFaqRoute: typeof CommonFaqRoute
  CommonNotificationsRoute: typeof CommonNotificationsRoute
  CommonSettingsRoute: typeof CommonSettingsRouteWithChildren
}

const CommonRouteChildren: CommonRouteChildren = {
  CommonSubscriptionsRoute: CommonSubscriptionsRouteWithChildren,
  CommonComplaintsRequestsRoute: CommonComplaintsRequestsRouteWithChildren,
  CommonFaqRoute: CommonFaqRoute,
  CommonNotificationsRoute: CommonNotificationsRoute,
  CommonSettingsRoute: CommonSettingsRouteWithChildren,
}

const CommonRouteWithChildren =
  CommonRoute._addFileChildren(CommonRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AnonRoute: AnonRouteWithChildren,
  CommonRoute: CommonRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
