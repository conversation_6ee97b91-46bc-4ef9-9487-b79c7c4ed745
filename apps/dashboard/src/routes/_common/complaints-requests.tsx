import { subscriptionsApi } from '@mass/api'
import { ComplaintFiltersModal, ComplaintsLayout, complaints$, subscription$ } from '@mass/components/dashboard'
import { createFileRoute, Outlet } from '@tanstack/react-router'

function RouteComponent() {
  return (
    <>
      <ComplaintsLayout>
        <Outlet />
      </ComplaintsLayout>
      <ComplaintFiltersModal />
    </>
  )
}

export const Route = createFileRoute('/_common/complaints-requests')({
  async beforeLoad(ctx) {
    if (ctx.cause === 'stay') {
      return
    }

    await complaints$.fetch()

    const regions = await subscriptionsApi.regions()
    subscription$.regions.set(regions)
  },

  component: RouteComponent,
})
