/** biome-ignore-all lint/nursery/noExcessiveLinesPerFunction: Redundant */
import { use$ } from '@legendapp/state/react'
import { complaintsApi, global$ } from '@mass/api'
import { complaints$, subscription$ } from '@mass/components/dashboard'
import { Link, Text, Title } from '@mass/components/shared'
import { ExternalIcon } from '@mass/icons'
import { type BlobType, i18n, useDate } from '@mass/utils'
import { createFileRoute } from '@tanstack/react-router'
import clsx from 'clsx'
import { useTranslation } from 'react-i18next'

function ComplaintDetails() {
  const { t: common } = useTranslation('common')
  const complaint = use$(() => complaints$.selectedComplaint.get())
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')
  const format = useDate('DD MMMM YYYY')

  const targetSubscription = use$(() =>
    complaints$.subscriptions
      .find(subscription => subscription.id.get() === complaints$.selectedComplaint.get()?.subscriptionId)
      ?.get(),
  )

  const targetDistributionCompany = use$(() =>
    subscription$.regions.get()?.find(region => region.id === targetSubscription?.regionId),
  )

  const targetCategory = use$(() =>
    complaints$.categories
      .find(
        c =>
          c.subtype.get() === complaints$.selectedComplaint.get()?.subtype &&
          c.type.get() === complaints$.selectedComplaint.get()?.type,
      )
      ?.get(),
  )

  const applicationInfosEntries = [
    {
      label: common('application-no'),
      value: complaint?.id,
    },
    {
      label: common('application-type'),
      value: targetCategory?.parentLabel[language.toLowerCase() as 'tr' | 'en'],
    },
    {
      label: common('application-category'),
      value: targetCategory?.label[language.toLowerCase() as 'tr' | 'en'],
    },
    {
      label: common('description'),
      value: complaint?.body ?? '',
    },
    {
      label: common('application-date'),
      value: format(complaint?.createdAt ?? ''),
    },
    {
      label: common('response-date'),
      value: complaint?.updatedAt ? format(complaint.updatedAt) : '-',
    },
    {
      label: common('attachments'),
      el:
        (complaint?.documents.length ?? 0) > 0 ? (
          complaint?.documents.map(document => (
            <Link
              variant='bordered-slim'
              to={document.url as BlobType}
              target='_blank'
              key={document.id}
              className='w-max'>
              {document.name}

              <ExternalIcon width={16} height={16} />
            </Link>
          ))
        ) : (
          <Text variant='dim-2'> - </Text>
        ),
    },
  ]

  const subscriptionInfosEntries = [
    {
      label: common('subscription-name'),
      value: targetSubscription?.name,
    },
    {
      label: common('distribution-company'),
      value: targetDistributionCompany?.name,
    },
  ]

  return (
    <div
      className={clsx(
        'flex flex-col', // flex
        'gap-8 p-8 md:gap-10 md:p-16', // spacing
      )}>
      <div
        className={clsx(
          'flex flex-col', // flex
          'rounded-b2 border border-accessory-1', // border
          'relative w-full overflow-hidden',
        )}>
        <div
          className={clsx(
            'flex items-center',
            'px-10 py-8', // spacing
            'bg-primary-light', // styling
            'border-accessory-1 border-b',
          )}>
          <Title variant='primary'> {common('application-informations')} </Title>
        </div>
        {applicationInfosEntries.map((entry, index) => (
          <div
            key={entry.label}
            className={clsx(
              'flex flex-col justify-center',
              'gap-2 px-10 py-6', // spacing
              {
                'border-accessory-1 border-b': index !== applicationInfosEntries.length - 1,
              },
            )}>
            <Title variant='h5' el='h2'>
              {entry.label}
            </Title>
            {entry.value && <Text variant='dim-2'>{entry.value}</Text>}
            {entry.el ?? null}
          </div>
        ))}
      </div>

      <div
        className={clsx(
          'flex flex-col', // flex
          'rounded-b2 border border-accessory-1', // border
          'relative w-full overflow-hidden',
        )}>
        <div
          className={clsx(
            'flex items-center',
            'px-10 py-8', // spacing
            'bg-primary-light', // styling
            'border-accessory-1 border-b',
          )}>
          <Title variant='primary'> {common('subscription-informations')} </Title>
        </div>
        {subscriptionInfosEntries.map((entry, index) => (
          <div
            key={entry.label}
            className={clsx(
              'flex flex-col justify-center',
              'gap-2 px-10 py-6', // spacing
              {
                'border-accessory-1 border-b': index !== subscriptionInfosEntries.length - 1,
              },
            )}>
            <Title variant='h5' el='h2'>
              {entry.label}
            </Title>
            {entry.value && <Text variant='dim-2'>{entry.value}</Text>}
          </div>
        ))}
      </div>
    </div>
  )
}

export const Route = createFileRoute('/_common/complaints-requests/$complaintId')({
  component: ComplaintDetails,

  async beforeLoad({ params }) {
    const complaintId = params.complaintId

    const complaint = await complaintsApi.detail({
      query: {
        id: complaintId,
      },
    })

    complaints$.selectedComplaint.set(complaint)
  },
})
