import { UploadIcon } from '@mass/icons'
import clsx from 'clsx'
import { useId, useState } from 'react'
import { useTranslation } from 'react-i18next'

interface FileUploaderProps {
  label?: string
  required?: boolean
  multiple?: boolean
  maxSize?: number // in bytes
  onChange?: (files: File[]) => void
  onFileProcess?: (file: File) => Promise<string | number>
  accept?: string
  className?: string
}

export function FileUploader({
  label,
  required = false,
  multiple = false,
  maxSize = 10 * 1024 * 1024, // Default 10MB
  onChange,
  onFileProcess,
  accept = 'application/pdf',
  className,
}: FileUploaderProps): React.ReactElement {
  const id = useId()

  const [files, setFiles] = useState<File[]>([])
  const { t: common } = useTranslation('common')

  const handleFilesChange = async (selectedFiles: File[]) => {
    const validFiles = selectedFiles.filter(file => accept.includes(file.type) && file.size <= maxSize)

    if (validFiles.length === 0) {
      return
    }

    const updatedFiles = multiple ? [...files, ...validFiles] : validFiles
    setFiles(updatedFiles)

    if (onChange) {
      onChange(updatedFiles)
    }

    if (onFileProcess) {
      await Promise.all(updatedFiles.map(file => onFileProcess(file)))
    }
  }

  return (
    <div className={className}>
      {label && (
        <div className='mb-6 font-medium text-black text-xs'>
          {label}
          {required && <span className='ml-4 text-error'>*</span>}
        </div>
      )}
      <div>
        <div className='rounded-b1 border border-accessory-1 border-dashed px-8 py-20'>
          <UploadIcon className='mx-auto h-24 w-24 text-dim-3' />

          <div className='mt-16 flex justify-center text-center'>
            <label
              htmlFor={id}
              className={clsx(
                'relative cursor-pointer rounded-b1 font-semibold text-black',
                'transition duration-200',
                'focus-within:outline-none focus-within:outline-2 focus-within:outline-primary focus-within:outline-offset-2 hover:text-primary/80',
              )}>
              <span className='text-xs'>{common('select-file')}</span>
              <input
                type='file'
                accept={accept}
                multiple={multiple}
                onChange={e => {
                  if (e.target.files) {
                    void handleFilesChange(Array.from(e.target.files))
                  }
                }}
                className='hidden'
                id={id}
              />
              <p className='mt-4 text-2xs text-dim-3'>
                {common('allowed-file-formats', { size: maxSize / 1024 / 1024 })}
              </p>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}
