import { use$ } from '@legendapp/state/react'
import { complaintsApi, global$ } from '@mass/api'
import { ArchiveIcon, DotsVerticalIcon } from '@mass/icons'
import { i18n, useDate } from '@mass/utils'
import { useNavigate } from '@tanstack/react-router'
import { type FC, memo } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Popover, Table, type TableColumnOptions, Text } from '../../shared'
import { complaints$ } from '../stores/complaints'

const useTableColumns = () => {
  const { t: common } = useTranslation('common')
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')
  const categories = use$(() => complaints$.categories.get())
  const subscriptions = use$(() => complaints$.subscriptions.get() ?? [])
  const isActive = use$(() => complaints$.query.values.status.get() === 'active')

  const format = useDate('DD MMMM YYYY HH:mm')

  return [
    {
      key: 'id',
      label: common('application-no'),

      render(value) {
        return (
          <div className='flex items-center gap-4'>
            <Text variant='dim'> {value} </Text>
          </div>
        )
      },
    },
    {
      key: 'subscriptionId',
      label: common('subscription'),

      render(value) {
        console.log(subscriptions, value)

        return <Text variant='dim'> {subscriptions.find(subscription => subscription.id === value)?.name ?? '-'} </Text>
      },
    },
    {
      key: 'subtype',
      label: common('application-category'),

      render(value) {
        const category = categories.find(c => c.subtype === value)

        return <Text variant='dim'> {category?.label[language.toLowerCase() as 'tr' | 'en']} </Text>
      },
    },
    {
      key: 'status',
      label: common('status'),

      render(value) {
        return <Text variant='dim'> {common(value.toLowerCase())} </Text>
      },
    },
    {
      key: 'createdAt',
      label: common('application-date'),

      render(value) {
        return <Text variant='dim'> {format(value)} </Text>
      },

      sortable: (rowA, rowB) => {
        const a = +new Date(rowA.original.createdAt)
        const b = +new Date(rowB.original.createdAt)

        return a - b
      },
    },
    {
      key: 'updatedAt',
      label: common('response-date'),

      render(value) {
        return <Text variant='dim'> {value ? format(value) : '-'} </Text>
      },

      sortable: (rowA, rowB) => {
        const a = rowA.original.updatedAt ? +new Date(rowA.original.updatedAt) : 0
        const b = rowB.original.updatedAt ? +new Date(rowB.original.updatedAt) : 0

        return a - b
      },
    },
    ...(isActive
      ? [
          {
            key: () => 'actions',
            label: common('actions'),

            render(_value, row) {
              return (
                <Popover
                  variant='icon-slim-bordered'
                  popoverPosition='bottom end'
                  className='flex justify-end'
                  buttonContent={() => <DotsVerticalIcon className='text-dim-3' />}>
                  <Button
                    variant='hover-error-borderless'
                    className='justify-between!'
                    onClick={async e => {
                      e.stopPropagation()

                      if (row.status === 'PENDING') {
                        await complaintsApi.cancel({
                          query: {
                            id: row.id,
                          },
                        })

                        await complaints$.query.filter()
                      }
                    }}>
                    {common('cancel')}

                    <ArchiveIcon className='h-8 w-8' />
                  </Button>
                </Popover>
              )
            },
          } satisfies TableColumnOptions<Dashboard.Complaints['complaints'][0]>,
        ]
      : []),
  ] satisfies TableColumnOptions<Dashboard.Complaints['complaints'][0]>[]
}

export const ComplaintResults: FC = memo(() => {
  const columns = useTableColumns()
  const navigate = useNavigate()

  return (
    <Table
      data={complaints$.complaints}
      columns={columns}
      selectable={row => {
        navigate({ to: '/complaints-requests/$complaintId', params: { complaintId: row.id }, viewTransition: true })
      }}
    />
  )
})
