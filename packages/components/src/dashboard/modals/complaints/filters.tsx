import { use$ } from '@legendapp/state/react'
import { global$ } from '@mass/api'
import { FilterLinesIcon } from '@mass/icons'
import { i18n, utcDate } from '@mass/utils'
import { type FC, memo } from 'react'
import { toast } from 'react-hot-toast'
import { useTranslation } from 'react-i18next'
import {
  Button,
  Combo,
  DatePicker,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  FileUploader,
  Text,
  ui$,
} from '../../../shared'
import { complaints$ } from '../../stores/complaints'
import { subscription$ } from '../../stores/subscription'

export const ComplaintFiltersModal: FC = memo(() => {
  const { t: dashboard } = useTranslation('dashboard')
  const { t: common } = useTranslation('common')

  const query = use$(() => complaints$.query.values.get())
  const subscriptions = use$(() => subscription$.subscriptions.get()?.content ?? [])
  const categories = use$(() => complaints$.categories.get())
  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')

  const handleClearFilters = async () => {
    try {
      complaints$.query.clear()

      await complaints$.query.filter()

      ui$.onChangeModal('dashboard.complaints-filters', false)
      toast.success(common('filters-cleared'))
    } catch (err) {
      console.log(err)
      toast.error(common('something-went-wrong'))
    }
  }

  const handleFilter = async () => {
    try {
      await complaints$.query.filter()

      ui$.onChangeModal('dashboard.complaints-filters', false)
      toast.success(common('filters-applied'))
    } catch (err) {
      toast.error(common('something-went-wrong'))
    }
  }

  return (
    <DialogRoot name='dashboard.complaints-filters'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent
          className='sm:max-h-[400px]! sm:max-w-[450px]'
          header={
            <DialogHeader
              icon={<FilterLinesIcon strokeWidth={2} className='h-12 w-12' />}
              title={dashboard('complaints-requests.filters')}
            />
          }
          footer={
            <DialogFooter slim={false} borderLess className='flex-row gap-4'>
              <Button variant='bordered' onClick={handleClearFilters}>
                {common('clear-filters')}
              </Button>
              <Button variant='primary' onClick={handleFilter}>
                {common('filter')}
              </Button>
            </DialogFooter>
          }>
          {/* selected Dates */}

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('date-range')} </Text>

            <DatePicker
              mode='range'
              minDate={utcDate().subtract(1, 'year').toDate()}
              maxDate={new Date()}
              placeholder={common('date-range')}
              value={query.selectedDate}
              onChange={value => complaints$.query.setSelectedDate(value)}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('subscription')} </Text>

            <Combo
              value={query.subscriptionId}
              options={[
                { value: 'all', label: common('all') },
                ...subscriptions.map(subscription => ({ value: subscription.id, label: subscription.name })),
              ]}
              onValueChange={value => complaints$.query.setSubscriptionId(value)}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <Text variant='subtitle'> {common('application-category')} </Text>

            <Combo
              value={query.category}
              options={[
                { value: 'all', label: common('all') },
                ...categories.map(category => ({
                  value: category.subtype ?? category.type,
                  label: category.label[language.toLowerCase() as 'tr' | 'en'],
                })),
              ]}
              onValueChange={value => complaints$.query.setCategory(value)}
              className='max-w-[225px]'
            />
          </div>

          <div className='mb-8 flex items-center justify-between gap-4'>
            <FileUploader className='w-full' />
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
})
