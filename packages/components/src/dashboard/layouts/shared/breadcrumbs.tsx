import { use$ } from '@legendapp/state/react'
import { global$ } from '@mass/api'
import { ChevronDownIcon, FileIcon, HomeIcon, NotificationIcon, SettingsIcon } from '@mass/icons'
import { i18n } from '@mass/utils'
import { useLocation } from '@tanstack/react-router'
import clsx from 'clsx'
import { type FC, Fragment, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Link, Text } from '../../../shared'
import { COMPLAINTS_REQUESTS_DETAILS_REGEX, SUBSCRIPTION_REGEX } from '../../hooks/use-meta'
import { complaints$ } from '../../stores/complaints'
import { subscription$ } from '../../stores/subscription'

export const Breadcrumbs: FC = () => {
  const { t: common } = useTranslation('common')
  const { t: dashboard } = useTranslation('dashboard')
  const { pathname } = useLocation()

  const language = use$(() => global$.language.get() ?? i18n.languages[0] ?? 'tr')
  const subscription = use$(() => subscription$.selectedSubscription.get())

  const complaintCategories = use$(() => complaints$.categories.get())
  const selectedComplaint = use$(() => complaints$.selectedComplaint.get())

  const el = useMemo(() => {
    const list: React.ReactNode[] = []

    let isHome = false

    if (pathname === '/' || SUBSCRIPTION_REGEX.test(pathname)) {
      isHome = true
    }

    if (isHome) {
      list.unshift(
        <Link to='/'>
          <Text variant='dim-2-semibold'> {dashboard('subscriptions.title')} </Text>
        </Link>,
      )
      list.unshift(<HomeIcon className='text-dim-2' strokeWidth={1.5} />)
    }

    if (pathname.startsWith('/settings')) {
      list.push(<SettingsIcon className='text-dim-2' strokeWidth={1.5} />)

      list.push(
        <Link to='/settings'>
          <Text variant='dim-2-semibold'> {common('settings')} </Text>
        </Link>,
      )
    }

    if (pathname.startsWith('/notifications')) {
      list.push(<NotificationIcon className='text-dim-2' strokeWidth={1.5} />)

      list.push(
        <Link to='/notifications'>
          <Text variant='dim-2-semibold'> {dashboard('notifications.title')} </Text>
        </Link>,
      )
    }

    if (pathname.startsWith('/complaints-requests')) {
      list.push(<FileIcon className='text-dim-2' strokeWidth={1.5} />)

      list.push(
        <Link to='/complaints-requests'>
          <Text variant='dim-2-semibold'> {dashboard('complaints-requests.title')} </Text>
        </Link>,
      )
    }

    if (COMPLAINTS_REQUESTS_DETAILS_REGEX.test(pathname)) {
      const foundCategory = complaintCategories.find(
        c => c.subtype === selectedComplaint?.subtype && c.type === selectedComplaint?.type,
      )

      list.push(
        <Link to='/complaints-requests/$complaintId' params={{ complaintId: selectedComplaint?.id }}>
          <Text variant='dim-2-semibold'>
            {dashboard('complaints-requests.x', {
              name: foundCategory?.label[language as 'tr' | 'en'],
              type: foundCategory?.parentLabel[language as 'tr' | 'en'],
            })}
          </Text>
        </Link>,
      )
    }

    if (SUBSCRIPTION_REGEX.test(pathname)) {
      list.push(
        <Link to='/$subscriptionId' params={{ subscriptionId: subscription?.id }}>
          <Text variant='dim-2-semibold'> {subscription?.name} </Text>
        </Link>,
      )
    }

    return list.map((item, index) => (
      <Fragment
        // biome-ignore lint/suspicious/noArrayIndexKey: Redundant
        key={`subscription-Breadcrumbs-${index}`}>
        {item}
        {index !== list.length - 1 && <ChevronDownIcon className='-rotate-90 text-dim-3' width={16} height={16} />}
      </Fragment>
    ))
  }, [pathname, dashboard, subscription, common, complaintCategories, selectedComplaint, language])

  return (
    <section
      className={clsx(
        'flex items-center gap-4', // flex
      )}>
      {el}
    </section>
  )
}
