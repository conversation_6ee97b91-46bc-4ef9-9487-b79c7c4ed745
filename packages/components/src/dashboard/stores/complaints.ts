import { ObservableHint, observable } from '@legendapp/state'
import { complaintsApi, subscriptionsApi } from '@mass/api'
import { type } from 'arktype'

export const complaintQueryValidation = type({
  status: "'active' | 'archived'",
  selectedDate: type('Date').array().atMostLength(2).atLeastLength(2).or(type('Date').array().atMostLength(0)),
  subscriptionId: 'string',
  category: 'string',
})

export const complaints$ = observable<Dashboard.Complaints>({
  categories: [],
  complaints: [],
  subscriptions: [],
  selectedComplaint: null,

  fetch: ObservableHint.function(async () => {
    const categories = await complaintsApi.categories()

    const normalizedCategories = Object.entries(categories.value).reduce(
      (acc, [key, value]) => {
        if (value.subcategories) {
          for (const [subKey, subValue] of Object.entries(value.subcategories)) {
            acc.push({
              type: key,
              subtype: subKey,
              label: {
                tr: subValue.label.TR,
                en: subValue.label.EN,
              },

              parentLabel: {
                tr: value.label.TR,
                en: value.label.EN,
              },
            })
          }
        } else {
          acc.push({
            type: key,
            label: {
              tr: value.label.TR,
              en: value.label.EN,
            },

            parentLabel: {
              tr: value.label.TR,
              en: value.label.EN,
            },
          })
        }

        return acc
      },
      [] as Dashboard.Complaints['categories'],
    )

    const { content: complaints } = await complaintsApi.list({
      invalidateCache: true,

      params: {
        pageSize: '100',
        'filter:eq': [
          {
            status: 'PENDING',
          },
        ],
        orderBy: 'createdAt:desc',
      },
    })

    const subscriptions = await Promise.all(
      complaints.map(complaint =>
        subscriptionsApi.detail({
          query: {
            id: complaint.subscriptionId,
          },
        }),
      ),
    )

    complaints$.subscriptions.set(subscriptions)
    complaints$.categories.set(normalizedCategories)
    complaints$.complaints.set(complaints)
  }),

  query: {
    values: {
      status: 'active',
      selectedDate: [],
      subscriptionId: 'all',
      category: 'all',
    },

    errors: {
      status: false,
      selectedDate: false,
      subscriptionId: false,
      category: false,
    },

    dirty: {
      status: false,
      selectedDate: false,
      subscriptionId: false,
      category: false,
    },

    setStatus: ObservableHint.function(status => {
      complaints$.query.values.status.set(status)
      complaints$.query.setDirty('status')
    }),

    setSelectedDate: ObservableHint.function(selectedDate => {
      complaints$.query.values.selectedDate.set(selectedDate)
      complaints$.query.setDirty('selectedDate')
    }),

    setSubscriptionId: ObservableHint.function(subscriptionId => {
      complaints$.query.values.subscriptionId.set(subscriptionId)
      complaints$.query.setDirty('subscriptionId')
    }),

    setCategory: ObservableHint.function(category => {
      complaints$.query.values.category.set(category)
      complaints$.query.setDirty('category')
    }),

    setDirty: ObservableHint.function(key => {
      complaints$.query.errors[key].set(
        complaintQueryValidation.get(key)(complaints$.query.values[key].get()) instanceof type.errors,
      )

      if (complaintQueryValidation.get(key)(complaints$.query.values[key].get()) instanceof type.errors) {
        console.log(complaintQueryValidation.get(key)(complaints$.query.values[key].get()))
      }

      complaints$.query.dirty[key].set(true)
    }),

    checkAll: ObservableHint.function(() => {
      complaints$.query.setDirty('status')

      const out = complaintQueryValidation(complaints$.query.values.get())

      if (out instanceof type.errors) {
        console.log(out)
      }
    }),

    clear: ObservableHint.function(() => {
      complaints$.query.values.set({
        status: 'active',
        selectedDate: [],
        subscriptionId: 'all',
        category: 'all',
      })

      complaints$.query.errors.set({
        status: false,
        selectedDate: false,
        subscriptionId: false,
        category: false,
      })

      complaints$.query.dirty.set({
        status: false,
        selectedDate: false,
        subscriptionId: false,
        category: false,
      })
    }),

    filter: ObservableHint.function(async () => {
      const query = complaints$.query.values.get()

      const filters = [] as Exclude<Api.ExtractParams<Api.Services['complaints']['list']>['filter:eq'], undefined>

      if (query.status === 'active') {
        filters.push({ status: 'PENDING' })
      } else if (query.status === 'archived') {
        filters.push({ status: 'CANCELED' })
        filters.push({ status: 'RESOLVED' })
      }

      if (query.subscriptionId !== 'all') {
        filters.push({ subscriptionId: query.subscriptionId })
      }

      if (query.category !== 'all') {
        filters.push({ subtype: query.category })
      }

      const bwFilters = [] as Exclude<Api.ExtractParams<Api.Services['complaints']['list']>['filter:bw'], undefined>

      if (query.selectedDate.length >= 2) {
        bwFilters.push({
          // biome-ignore lint/style/noNonNullAssertion: Redundant
          createdAt: `${query.selectedDate[0]!.toISOString()},${query.selectedDate[1]!.toISOString()}`,
        })
      }

      const { content: complaints } = await complaintsApi.list({
        params: {
          pageSize: '100',

          'filter:eq': filters,
          'filter:bw': bwFilters,
          orderBy: 'createdAt:desc',
        },
        invalidateCache: true,
      })

      complaints$.complaints.set(complaints)
      // ui$.onChangeModal('dashboard.complaints-filters', false)
    }),
  },
})
